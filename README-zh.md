# SmartEngine

SmartEngine 是一款轻量级业务编排引擎，在阿里巴巴集团内部广泛使用。  
它可用于微服务架构中的多服务编排，以高性能、低存储成本的方式启动/触发流程实例，同时也能应用于传统审批流程场景。

[English](./README.md)

## 设计理念

0. KISS 原则（保持简单）
1. 标准化：遵循 BPMN2.0 规范，统一领域语言
2. 可扩展性：支持解析器、行为、存储、用户集成等模块的灵活扩展
3. 高性能：针对简单流程场景提供优化方案，提升性能并降低存储成本
4. 低依赖：从设计之初就极力避免 JAR 地狱问题

## 核心特性

0. 采用 CQRS 风格的 API 来启动、触发、查询流程实例、任务和活动
1. 支持基础 BPMN 元素：开始事件/结束事件/顺序流/独占网关/并行网关/包容网关/服务任务/接收任务
2. 针对简单流程场景提供性能优化和存储成本降低方案
3. 其他功能：流程跳转、变量持久化、任务分配器、会签

## 文档

- [文档首页](https://github.com/alibaba/SmartEngine/wiki)

## 开源协议

SmartEngine 采用 Apache 2.0 协议开源

## 联系我们

| 微信交流群                                                   | 个人微信号                                                   |
|-------------------------------------------------------------|-------------------------------------------------------------|
| <img src="./docs/contact/group.jpg" style="max-width:200px;"/> | <img src="./docs/contact/me.jpg" style="max-width:200px;"/> |

## 致谢

受 Activiti、MyBatis、Netty 等项目启发