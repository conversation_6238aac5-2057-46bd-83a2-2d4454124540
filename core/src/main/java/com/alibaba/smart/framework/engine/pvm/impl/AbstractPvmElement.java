package com.alibaba.smart.framework.engine.pvm.impl;

import com.alibaba.smart.framework.engine.model.assembly.ExtensionElementContainer;
import com.alibaba.smart.framework.engine.pvm.PvmElement;

/**
 * <AUTHOR> 帝奇  2016.11.11
 * <AUTHOR> 2016.04.13
 */
public abstract class AbstractPvmElement<M extends ExtensionElementContainer> implements PvmElement<M> {


    private M model;

    @Override
    public M getModel() {
        return this.model;
    }

    public void setModel(M model) {
        this.model = model;
    }


}
