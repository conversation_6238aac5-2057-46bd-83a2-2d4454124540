package com.alibaba.smart.framework.engine.model.instance;

/**
 * <AUTHOR> 帝奇  2016.11.11
 * <AUTHOR> 2016.04.13
 */
public interface TaskAssigneeInstance extends LifeCycleInstance {

    String getProcessInstanceId();

    void setProcessInstanceId(String processInstanceId);

    String getTaskInstanceId();

    void setTaskInstanceId(String taskInstanceId);

     String getAssigneeId() ;

     void setAssigneeId(String assigneeId);

     String getAssigneeType();

     void setAssigneeType(String assigneeType);

}
