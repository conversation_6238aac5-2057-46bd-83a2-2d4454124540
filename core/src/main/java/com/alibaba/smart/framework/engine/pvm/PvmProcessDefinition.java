package com.alibaba.smart.framework.engine.pvm;

import java.util.Map;

/**
 * <AUTHOR> 帝奇  2016.11.11
 * <AUTHOR> 2016.04.13
 */
public interface PvmProcessDefinition {

    String getId();

    String getVersion();

    String getTenantId();

    String getIdAndVersion();

    void setIdAndVersion(String idAndVersion);

    PvmActivity getStartActivity();

    Map<String, PvmActivity> getActivities();

}
