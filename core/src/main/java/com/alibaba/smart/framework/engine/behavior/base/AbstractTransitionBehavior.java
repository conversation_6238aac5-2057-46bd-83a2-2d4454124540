package com.alibaba.smart.framework.engine.behavior.base;

import com.alibaba.smart.framework.engine.behavior.TransitionBehavior;
import com.alibaba.smart.framework.engine.context.ExecutionContext;
import com.alibaba.smart.framework.engine.model.assembly.Transition;
import com.alibaba.smart.framework.engine.pvm.PvmTransition;

/**
 * <AUTHOR> 帝奇  2016.11.11
 * <AUTHOR> 2016.04.13
 */
public abstract class AbstractTransitionBehavior<T extends Transition> implements TransitionBehavior {

    private PvmTransition runtimeTransition;



    @Override
    public boolean match(ExecutionContext context, Transition model) {
        return false;
    }



    protected T getModel() {
        return (T)this.runtimeTransition.getModel();
    }
}
