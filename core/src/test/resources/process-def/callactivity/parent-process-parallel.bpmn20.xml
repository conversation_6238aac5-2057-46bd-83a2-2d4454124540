<?xml version="1.0" encoding="UTF-8"?>
<definitions id="parent-process" version="1.0.0"
             xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             targetNamespace="Examples">

    <process id="parent-process" isExecutable="true">
        <startEvent id="start"/>

        <sequenceFlow id="flow_1" sourceRef="start" targetRef="task_1"/>

        <receiveTask id="task_1" name="Task 1" />

        <sequenceFlow id="to_fork_1" sourceRef="task_1" targetRef="fork_1"/>

        <!-- ======== Parallel 1 ======== -->
        <parallelGateway id="fork_1" />

        <sequenceFlow id="flow_fork_1_task" sourceRef="fork_1" targetRef="parallel_1_task"/>
        <receiveTask id="parallel_1_task" name="Parallel Task 1" />
        <sequenceFlow id="flow_join_1_task" sourceRef="parallel_1_task" targetRef="join_1"/>

        <sequenceFlow id="flow_fork_1_subprocess" sourceRef="fork_1" targetRef="parallel_1_subprocess"/>
        <callActivity id="parallel_1_subprocess" name="Parallel Sub-Process 1" calledElement="sub-process" calledElementVersion="1.0.0" />
        <sequenceFlow id="flow_join_1_subprocess" sourceRef="parallel_1_subprocess" targetRef="join_1"/>

        <parallelGateway id="join_1" />
        <!-- ======== Parallel 1 end ======== -->


        <sequenceFlow id="flow_2" sourceRef="join_1" targetRef="task_2"/>

        <receiveTask id="task_2" name="Task 2" />

        <sequenceFlow id="to_fork_2" sourceRef="task_2" targetRef="fork_2"/>

        <!-- ======== Parallel 2 (suspend) ======== -->
        <parallelGateway id="fork_2" />

        <sequenceFlow id="flow_fork_2_task" sourceRef="fork_2" targetRef="parallel_2_task"/>
        <receiveTask id="parallel_2_task" name="Parallel Task 2" />
        <sequenceFlow id="flow_join_2_task" sourceRef="parallel_2_task" targetRef="join_2"/>

        <sequenceFlow id="flow_fork_2_subprocess" sourceRef="fork_2" targetRef="parallel_2_subprocess"/>
        <callActivity id="parallel_2_subprocess" name="Parallel Sub-Process 2" calledElement="sub-process-suspend" calledElementVersion="1.0.0" />
        <sequenceFlow id="flow_join_2_subprocess" sourceRef="parallel_2_subprocess" targetRef="join_2"/>

        <parallelGateway id="join_2" />
        <!-- ======== Parallel 2 end ======== -->

        <sequenceFlow id="flow_3" sourceRef="join_2" targetRef="task_3"/>

        <receiveTask id="task_3" name="Task 3" />

        <sequenceFlow id="to_end" sourceRef="task_3" targetRef="end"/>

        <endEvent id="end" name="End"/>

    </process>

</definitions>