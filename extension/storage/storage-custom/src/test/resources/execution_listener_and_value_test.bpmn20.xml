<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns:smart="http://smartengine.org/schema/process"
             xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" id="test-exclusive" targetNamespace="Examples"
             version="1.0.0">

    <process id="demo" version="1.0.0" name="Process Demo">

        <startEvent id="theStart">
            <extensionElements>
                <smart:executionListener event="PROCESS_START"  class="com.alibaba.smart.framework.engine.test.ProcessStartListener"/>
            </extensionElements>
        </startEvent>


        <sequenceFlow id="flow1" sourceRef="theStart" targetRef="create"/>


        <serviceTask id="create" name="Create" smart:class="com.alibaba.smart.framework.engine.test.delegation.Tracker">
            <extensionElements>
                <smart:value name="task1" value="Create task"/>
                <!-- 测试，这里并不会触发  PROCESS_START -->
                <smart:executionListener event="PROCESS_START"  class="com.alibaba.smart.framework.engine.test.ProcessStartListener"/>
                <smart:executionListener event="ACTIVITY_START"  class="com.alibaba.smart.framework.engine.test.StartListener"/>
                <smart:executionListener event="ACTIVITY_END"  class="com.alibaba.smart.framework.engine.test.EndListener"/>
                <!-- 测试，这里并不会触发 PROCESS_END -->
                <smart:executionListener event="PROCESS_END"  class="com.alibaba.smart.framework.engine.test.ProcessEndListener"/>
            </extensionElements>
        </serviceTask>

        <sequenceFlow id="flow2" sourceRef="create" targetRef="pay"/>

        <serviceTask id="pay" name="Pay" smart:class="com.alibaba.smart.framework.engine.test.delegation.Tracker">
            <extensionElements>
                <smart:value name="task2" value="Pay task"/>
                <smart:properties>
                    <smart:value name="from" value="koubei" />
                </smart:properties>
            </extensionElements>
        </serviceTask>

        <sequenceFlow id="flow3" sourceRef="pay" targetRef="theEnd"/>

        <endEvent id="theEnd">
            <extensionElements>
                <smart:executionListener event="PROCESS_END"  class="com.alibaba.smart.framework.engine.test.ProcessEndListener"/>
            </extensionElements>
        </endEvent>

    </process>

</definitions>
