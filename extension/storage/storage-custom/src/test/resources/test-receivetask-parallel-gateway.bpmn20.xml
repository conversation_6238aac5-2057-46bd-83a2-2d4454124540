<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:smart="http://smartengine.org/schema/process" id="aa" targetNamespace="smart" exporter="Camunda Modeler" exporterVersion="4.7.0">
  <process id="exclusiveTest" isExecutable="false">
    <startEvent id="theStart" />
    <sequenceFlow id="flow1" sourceRef="theStart" targetRef="fork" />
    <parallelGateway id="fork" name="ForkGateway" />
    <sequenceFlow id="flow2" sourceRef="fork" targetRef="theTask1" />
    <sequenceFlow id="flow3" sourceRef="fork" targetRef="theTask2" />
    <receiveTask id="theTask1" name="Task1" smart:class="com.alibaba.smart.framework.engine.test.parallelgateway.single.thread.ServiceTaskDelegation" />
    <receiveTask id="theTask2" name="Task2" smart:class="com.alibaba.smart.framework.engine.test.parallelgateway.single.thread.ServiceTaskDelegation" />
    <sequenceFlow id="flow5" sourceRef="theTask1" targetRef="join" />
    <sequenceFlow id="flow6" sourceRef="theTask2" targetRef="join" />
    <parallelGateway id="join" name="JoinGateWay" />
    <sequenceFlow id="flow7" sourceRef="join" targetRef="theTask3" />
    <receiveTask id="theTask3" name="Task3" smart:class="com.alibaba.smart.framework.engine.test.parallelgateway.single.thread.ServiceTaskDelegation" />
    <sequenceFlow id="flow4" sourceRef="theTask3" targetRef="theEnd" />
    <endEvent id="theEnd" />
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="exclusiveTest">
      <bpmndi:BPMNEdge id="flow41" bpmnElement="flow4">
        <di:waypoint x="1074" y="280" />
        <di:waypoint x="1206" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="flow71" bpmnElement="flow7">
        <di:waypoint x="824" y="280" />
        <di:waypoint x="1026" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="flow61" bpmnElement="flow6">
        <di:waypoint x="604" y="140" />
        <di:waypoint x="800" y="140" />
        <di:waypoint x="800" y="256" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="flow51" bpmnElement="flow5">
        <di:waypoint x="604" y="400" />
        <di:waypoint x="800" y="400" />
        <di:waypoint x="800" y="304" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="flow31" bpmnElement="flow3">
        <di:waypoint x="360" y="256" />
        <di:waypoint x="360" y="140" />
        <di:waypoint x="556" y="140" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="flow21" bpmnElement="flow2">
        <di:waypoint x="360" y="304" />
        <di:waypoint x="360" y="400" />
        <di:waypoint x="556" y="400" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="flow11" bpmnElement="flow1">
        <di:waypoint x="204" y="280" />
        <di:waypoint x="336" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="fork1" bpmnElement="fork">
        <dc:Bounds x="336" y="256" width="48" height="48" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="394" y="273" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="theTask11" bpmnElement="theTask1">
        <dc:Bounds x="556" y="376" width="48" height="48" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="theTask21" bpmnElement="theTask2">
        <dc:Bounds x="556" y="116" width="48" height="48" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="join1" bpmnElement="join">
        <dc:Bounds x="776" y="256" width="48" height="48" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="700" y="273" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="theTask31" bpmnElement="theTask3">
        <dc:Bounds x="1026" y="256" width="48" height="48" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="theEnd1" bpmnElement="theEnd">
        <dc:Bounds x="1206" y="256" width="48" height="48" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="theStart1" bpmnElement="theStart">
        <dc:Bounds x="156" y="256" width="48" height="48" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
