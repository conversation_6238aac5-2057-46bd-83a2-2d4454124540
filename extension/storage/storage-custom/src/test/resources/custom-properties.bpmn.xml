<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns:smart="http://smartengine.org/schema/process"
             xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" id="custom-properties" targetNamespace="smart">
    <process id="Process_1" isExecutable="false">
        <startEvent id="StartEvent_1"/>
        <serviceTask id="serviceTask" smart:abc="abc"/>
        <sequenceFlow id="SequenceFlow_01ueswe" sourceRef="StartEvent_1" targetRef="serviceTask"/>
        <serviceTask id="userTask" smart:def="def"/>
        <sequenceFlow id="SequenceFlow_1borlkx" sourceRef="serviceTask" targetRef="userTask"/>
        <receiveTask id="receiveTask" smart:hij="hij"/>
        <sequenceFlow id="SequenceFlow_0ezdah5" sourceRef="userTask" targetRef="receiveTask"/>
        <endEvent id="end"/>
        <sequenceFlow id="SequenceFlow_1jp6b5l" sourceRef="receiveTask" targetRef="end"/>
    </process>

</definitions>
