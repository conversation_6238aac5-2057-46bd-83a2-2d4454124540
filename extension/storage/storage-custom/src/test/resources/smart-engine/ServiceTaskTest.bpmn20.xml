<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns:smart="http://smartengine.org/schema/process" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             id="ServiceOrchestrationTest"
             version="1.0.0"
             xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" targetNamespace="Examples">

    <process id="exclusiveTest">

        <startEvent id="theStart">
        </startEvent>

        <sequenceFlow id="flow1" sourceRef="theStart" targetRef="exclusiveGw1"/>

        <exclusiveGateway id="exclusiveGw1" name="Exclusive Gateway 1"/>

        <sequenceFlow id="flow2" sourceRef="exclusiveGw1"
                      targetRef="theTask1">
            <conditionExpression xsi:type="mvel">input == 1
            </conditionExpression>
        </sequenceFlow>

        <sequenceFlow id="flow3" sourceRef="exclusiveGw1"
                      targetRef="theTask2">
            <conditionExpression xsi:type="mvel">input == 2
            </conditionExpression>
        </sequenceFlow>

        <sequenceFlow id="flow4" sourceRef="exclusiveGw1"
                      targetRef="theTask3">
            <conditionExpression xsi:type="mvel">input == 3
            </conditionExpression>
        </sequenceFlow>

        <serviceTask id="theTask1" name="Task 1"
                     smart:class="com.alibaba.smart.framework.engine.test.orchestration.ServiceOrchestrationJavaDelegation">
        </serviceTask>

        <sequenceFlow id="flow5" sourceRef="theTask1" targetRef="selectTask"/>

        <serviceTask id="theTask2" name="Task 2"
                     smart:class="com.alibaba.smart.framework.engine.test.orchestration.ServiceOrchestrationJavaDelegation">
        </serviceTask>

        <sequenceFlow id="flow6" sourceRef="theTask2" targetRef="selectTask"/>

        <serviceTask id="theTask3" name="Task 3"
                     smart:class="com.alibaba.smart.framework.engine.test.orchestration.ServiceOrchestrationJavaDelegation">
        </serviceTask>

        <sequenceFlow id="flow7" sourceRef="theTask3" targetRef="selectTask"/>

        <serviceTask id="selectTask" name="Select Task"
                     smart:class="com.alibaba.smart.framework.engine.test.orchestration.ServiceOrchestrationJavaDelegation">
        </serviceTask>

        <sequenceFlow id="flow8" sourceRef="selectTask"
                      targetRef="exclusiveGw2"/>

        <exclusiveGateway id="exclusiveGw2" name="Exclusive Gateway 2"/>

        <sequenceFlow id="flow9" sourceRef="exclusiveGw2"
                      targetRef="theTask4">
            <conditionExpression type="mvel">input == 4
            </conditionExpression>
        </sequenceFlow>

        <sequenceFlow id="flow10" sourceRef="exclusiveGw2"
                      targetRef="theTask5">
            <conditionExpression type="mvel">input == 5
            </conditionExpression>
        </sequenceFlow>

        <sequenceFlow id="flow11" sourceRef="exclusiveGw2"
                      targetRef="theTask6">
            <conditionExpression type="mvel">input == 2
            </conditionExpression>
        </sequenceFlow>

        <serviceTask id="theTask4" name="Task 4"
                     smart:class="com.alibaba.smart.framework.engine.test.orchestration.ServiceOrchestrationJavaDelegation">
        </serviceTask>

        <sequenceFlow id="flow12" sourceRef="theTask4" targetRef="theEnd"/>

        <serviceTask id="theTask5" name="Task 5"
                     smart:class="com.alibaba.smart.framework.engine.test.orchestration.ServiceOrchestrationJavaDelegation">
        </serviceTask>

        <sequenceFlow id="flow13" sourceRef="theTask5" targetRef="theEnd"/>

        <serviceTask id="theTask6" name="Task 6"
                     smart:class="com.alibaba.smart.framework.engine.test.orchestration.ServiceOrchestrationJavaDelegation">
        </serviceTask>

        <sequenceFlow id="flow14" sourceRef="theTask6" targetRef="theEnd"/>

        <endEvent id="theEnd"/>

    </process>

</definitions>