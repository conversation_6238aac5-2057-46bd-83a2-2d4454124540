<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_12usa7w" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.6.0">
    <bpmn:process id="child" isExecutable="true" version="1">
        <bpmn:startEvent id="start">
            <bpmn:outgoing>toTask3</bpmn:outgoing>
        </bpmn:startEvent>
        <bpmn:sequenceFlow id="toTask3" sourceRef="start" targetRef="task_3" />
        <bpmn:receiveTask id="task_3" name="task_3">
            <bpmn:incoming>toTask3</bpmn:incoming>
            <bpmn:outgoing>toFork</bpmn:outgoing>
        </bpmn:receiveTask>
        <bpmn:sequenceFlow id="toFork" sourceRef="task_3" targetRef="childFork" />
        <bpmn:parallelGateway id="childFork">
            <bpmn:incoming>toFork</bpmn:incoming>
            <bpmn:outgoing>toTask4</bpmn:outgoing>
            <bpmn:outgoing>toTask5</bpmn:outgoing>
        </bpmn:parallelGateway>
        <bpmn:sequenceFlow id="toTask4" sourceRef="childFork" targetRef="task_4" />
        <bpmn:receiveTask id="task_4" name="task_4">
            <bpmn:incoming>toTask4</bpmn:incoming>
            <bpmn:outgoing>toJoinA</bpmn:outgoing>
        </bpmn:receiveTask>
        <bpmn:sequenceFlow id="toTask5" sourceRef="childFork" targetRef="task_5" />
        <bpmn:receiveTask id="task_5" name="task_5">
            <bpmn:incoming>toTask5</bpmn:incoming>
            <bpmn:outgoing>toJoinB</bpmn:outgoing>
        </bpmn:receiveTask>
        <bpmn:sequenceFlow id="toJoinB" sourceRef="task_5" targetRef="childJoin" />
        <bpmn:parallelGateway id="childJoin">
            <bpmn:incoming>toJoinB</bpmn:incoming>
            <bpmn:incoming>toJoinA</bpmn:incoming>
            <bpmn:outgoing>toEnd</bpmn:outgoing>
        </bpmn:parallelGateway>
        <bpmn:sequenceFlow id="toJoinA" sourceRef="task_4" targetRef="childJoin" />
        <bpmn:endEvent id="end">
            <bpmn:incoming>toEnd</bpmn:incoming>
        </bpmn:endEvent>
        <bpmn:sequenceFlow id="toEnd" sourceRef="childJoin" targetRef="end" />
    </bpmn:process>
    <bpmndi:BPMNDiagram id="BPMNDiagram_1">
        <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="child">
            <bpmndi:BPMNEdge id="Flow_0uucd4w_di" bpmnElement="toEnd">
                <di:waypoint x="735" y="177" />
                <di:waypoint x="792" y="177" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1195gc6_di" bpmnElement="toJoinA">
                <di:waypoint x="630" y="110" />
                <di:waypoint x="710" y="110" />
                <di:waypoint x="710" y="152" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1rvbxhk_di" bpmnElement="toJoinB">
                <di:waypoint x="630" y="260" />
                <di:waypoint x="710" y="260" />
                <di:waypoint x="710" y="202" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_06j2k78_di" bpmnElement="toTask5">
                <di:waypoint x="450" y="202" />
                <di:waypoint x="450" y="260" />
                <di:waypoint x="530" y="260" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1q2s98x_di" bpmnElement="toTask4">
                <di:waypoint x="450" y="152" />
                <di:waypoint x="450" y="110" />
                <di:waypoint x="530" y="110" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1szcu30_di" bpmnElement="toFork">
                <di:waypoint x="370" y="177" />
                <di:waypoint x="425" y="177" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1qc9zc4_di" bpmnElement="toTask3">
                <di:waypoint x="215" y="177" />
                <di:waypoint x="270" y="177" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="start">
                <dc:Bounds x="179" y="159" width="36" height="36" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1p4dvwh_di" bpmnElement="task_3">
                <dc:Bounds x="270" y="137" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_1inwo6z_di" bpmnElement="childFork">
                <dc:Bounds x="425" y="152" width="50" height="50" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_07euwv4_di" bpmnElement="task_4">
                <dc:Bounds x="530" y="70" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1mg78cq_di" bpmnElement="task_5">
                <dc:Bounds x="530" y="220" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_162rjn6_di" bpmnElement="childJoin">
                <dc:Bounds x="685" y="152" width="50" height="50" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_0nqgwsl_di" bpmnElement="end">
                <dc:Bounds x="792" y="159" width="36" height="36" />
            </bpmndi:BPMNShape>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</bpmn:definitions>
