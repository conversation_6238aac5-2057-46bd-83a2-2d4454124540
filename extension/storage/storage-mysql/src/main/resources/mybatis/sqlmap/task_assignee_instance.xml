<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alibaba.smart.framework.engine.persister.database.dao.TaskAssigneeDAO">

    <sql id="baseColumn">
        id, gmt_create, gmt_modified,process_instance_id,
        task_instance_id,assignee_id,assignee_type,tenant_id
    </sql>

    <insert id="insert" parameterType="com.alibaba.smart.framework.engine.persister.database.entity.TaskAssigneeEntity"
            keyProperty="id">
        insert into se_task_assignee_instance(<include refid="baseColumn"/>)
        values (#{id}, #{gmtCreate}, #{gmtModified}, #{processInstanceId},
        #{taskInstanceId},#{assigneeId},#{assigneeType},
        #{tenantId})
    </insert>

    <delete id="delete" parameterType="long">
        delete
        from se_task_assignee_instance
        where id = #{id}
        <if test="tenantId != null"> and tenant_id = #{tenantId} </if>
    </delete>

    <update id="update">
        update se_task_assignee_instance
        <set>
            gmt_modified=now(6)
            <if test="assigneeId != null">,assignee_id = #{assigneeId}</if>
            <!--<if test="assigneeType != null">,assignee_type = #{assigneeType}</if>-->
        </set>
        where id=#{id}
        <if test="tenantId != null"> and tenant_id = #{tenantId} </if>
    </update>

    <select id="findOne" resultType="com.alibaba.smart.framework.engine.persister.database.entity.TaskAssigneeEntity">
        select
        <include refid="baseColumn"/>
        from se_task_assignee_instance
        where id=#{id}
        <if test="tenantId != null"> and tenant_id = #{tenantId} </if>
    </select>

    <select id="findList" resultType="com.alibaba.smart.framework.engine.persister.database.entity.TaskAssigneeEntity">
        select
        <include refid="baseColumn"/>
        from se_task_assignee_instance
        where task_instance_id=#{taskInstanceId}
        <if test="tenantId != null"> and tenant_id = #{tenantId} </if>
    </select>

    <select id="findListForInstanceList" parameterType="java.util.List"
            resultType="com.alibaba.smart.framework.engine.persister.database.entity.TaskAssigneeEntity">
        select
        <include refid="baseColumn"/>
        from se_task_assignee_instance
        where task_instance_id in
        <foreach item="item" index="index" separator="," open="(" close=")" collection="taskInstanceIdList">
            #{item}
        </foreach>
        <if test="tenantId != null"> and tenant_id = #{tenantId} </if>
    </select>

</mapper>
