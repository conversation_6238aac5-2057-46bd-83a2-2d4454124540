<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:jdbc="http://www.springframework.org/schema/jdbc"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context
		http://www.springframework.org/schema/context/spring-context.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
		http://www.springframework.org/schema/jdbc http://www.springframework.org/schema/jdbc/spring-jdbc-4.2.xsd">


    <context:component-scan base-package="com.alibaba.smart.framework.engine.persister"/>
    <context:component-scan base-package="com.alibaba.smart.framework.engine.test"/>

    <!-- Mapper接口所在包名，Spring会自动查找其下的Mapper -->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.alibaba.smart.framework.engine.persister.database" />
    </bean>

    <!--START MYSQL DATASOURCE-->

<!--    <bean id="dataSource" class="org.springframework.jdbc.datasource.DriverManagerDataSource">-->
<!--        <property name="driverClassName" value="com.mysql.cj.jdbc.Driver"/>-->
<!--        <property name="url" value="****************************************************************************"/>-->
<!--        <property name="username" value="root"/>-->
<!--        <property name="password" value="ghj"/>-->
<!--    </bean>-->

    <!--END MYSQL DATASOURCE-->

    <!--START H2 DATASOURCE-->

    <bean id="dataSource" class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="url" value="jdbc:h2:mem:test;INIT=create schema if not exists test\;runscript from 'src/main/resources/sql/schema-h2-only-for-test.sql'"/>

    </bean>


<!--    <jdbc:embedded-database id="dataSource" type="H2">-->
<!--        <jdbc:script location="classpath:sql/schema.sql"  />-->
<!--    </jdbc:embedded-database>-->

    <!--END H2 DATASOURCE-->


    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean"
          p:dataSource-ref="dataSource"
          p:configLocation="classpath:mybatis/mybatis-test-config.xml"
          p:mapperLocations="classpath:mybatis/sqlmap/*.xml"/>

    <bean id="sqlSession" class="org.mybatis.spring.SqlSessionTemplate">
        <constructor-arg index="0" ref="sqlSessionFactory"/>
    </bean>

    <!-- 事务管理器 -->
    <bean id="txManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager"
          p:dataSource-ref="dataSource"/>



    <!-- 事物传播特性 -->
    <tx:advice id="txAdvice" transaction-manager="txManager">
        <tx:attributes>
            <tx:method name="create*" propagation="REQUIRED"/>
            <tx:method name="save*" propagation="REQUIRED"/>
            <tx:method name="insert*" propagation="REQUIRED"/>

            <tx:method name="delete*" propagation="REQUIRED"/>

            <tx:method name="modify*" propagation="REQUIRED"/>
            <tx:method name="update*" propagation="REQUIRED"/>
            <tx:method name="alter*" propagation="REQUIRED"/>
            <tx:method name="reset*" propagation="REQUIRED"/>

            <tx:method name="find*" propagation="REQUIRED"/>
            <tx:method name="query*" propagation="REQUIRED"/>
            <tx:method name="list*" propagation="REQUIRED"/>
            <tx:method name="count*" propagation="REQUIRED"/>

            <tx:method name="batchDelete*" propagation="REQUIRED"/>
            <tx:method name="batchInsert*" propagation="REQUIRED"/>
            <tx:method name="batchUpdate*" propagation="REQUIRED"/>

            <tx:method name="assign*" propagation="REQUIRED"/>
            <tx:method name="generate*" propagation="REQUIRED"/>
            <tx:method name="clear*" propagation="REQUIRED"/>


            <tx:method name="*" propagation="SUPPORTS" read-only="true"/>
        </tx:attributes>
    </tx:advice>




</beans>
