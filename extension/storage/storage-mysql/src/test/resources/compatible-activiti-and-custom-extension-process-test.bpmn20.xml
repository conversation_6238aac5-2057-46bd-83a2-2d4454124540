<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL"
                   xmlns:alibaba="http://alibaba.org/bpmn" xmlns:activiti="http://activiti.org/bpmn" id="sample-diagram"
                   targetNamespace="http://bpmn.io/schema/bpmn"
                   xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL BPMN20.xsd">
    <bpmn2:process id="Process_1">
        <bpmn2:startEvent id="StartEvent_0iws7oy">
            <bpmn2:outgoing>SequenceFlow_003qn0i</bpmn2:outgoing>
        </bpmn2:startEvent>

        <bpmn2:sequenceFlow id="SequenceFlow_003qn0i" sourceRef="StartEvent_0iws7oy" targetRef="userTask1"/>

        <bpmn2:userTask id="userTask1" name="1个">
            <bpmn2:multiInstanceLoopCharacteristics ></bpmn2:multiInstanceLoopCharacteristics>
        </bpmn2:userTask>

        <bpmn2:sequenceFlow id="SequenceFlow_04o344j" sourceRef="userTask1" targetRef="userTask2"/>

        <bpmn2:userTask  id="userTask2" name="2个">
            <bpmn2:incoming>SequenceFlow_04o344j</bpmn2:incoming>
            <bpmn2:outgoing>SequenceFlow_0vhfbz6</bpmn2:outgoing>
            <bpmn2:multiInstanceLoopCharacteristics >
                <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression"><![CDATA[${nrOfCompletedInstances + 1 >= 2 }]]></bpmn2:completionCondition>
            </bpmn2:multiInstanceLoopCharacteristics>
        </bpmn2:userTask>


        <bpmn2:sequenceFlow id="SequenceFlow_0vhfbz6" sourceRef="userTask2"
                            targetRef="ExclusiveGateway_0pjsqqm"/>

        <bpmn2:exclusiveGateway id="ExclusiveGateway_0pjsqqm">
            <bpmn2:incoming>SequenceFlow_0vhfbz6</bpmn2:incoming>
            <bpmn2:outgoing>SequenceFlow_1oi25aj</bpmn2:outgoing>
            <bpmn2:outgoing>SequenceFlow_1pshchp</bpmn2:outgoing>
        </bpmn2:exclusiveGateway>

        <bpmn2:sequenceFlow id="SequenceFlow_1oi25aj" sourceRef="ExclusiveGateway_0pjsqqm" targetRef="userTask3"
                            >
            <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">
                <![CDATA[${order.yzje>100}]]></bpmn2:conditionExpression>
        </bpmn2:sequenceFlow>
        <bpmn2:sequenceFlow id="SequenceFlow_1pshchp" sourceRef="ExclusiveGateway_0pjsqqm" targetRef="UserTask_1y14uzz"
                            >
            <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">
                <![CDATA[${order.yzje<100}]]></bpmn2:conditionExpression>
        </bpmn2:sequenceFlow>


        <bpmn2:userTask id="userTask3" name="职务1">
            <bpmn2:documentation>ds</bpmn2:documentation>
            <bpmn2:multiInstanceLoopCharacteristics >
                <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression">${nrOfCompletedInstances >= 1}
                </bpmn2:completionCondition>
            </bpmn2:multiInstanceLoopCharacteristics>
        </bpmn2:userTask>

        <bpmn2:sequenceFlow id="SequenceFlow_1wbvhjm" sourceRef="userTask3" targetRef="UserTask_0ixdrmt"/>

        <bpmn2:userTask id="UserTask_0ixdrmt" name="职务3">
            <bpmn2:incoming>SequenceFlow_1wbvhjm</bpmn2:incoming>
            <bpmn2:outgoing>SequenceFlow_0ujt88d</bpmn2:outgoing>
            <bpmn2:multiInstanceLoopCharacteristics >
                <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression">${nrOfCompletedInstances >= 1}
                </bpmn2:completionCondition>
            </bpmn2:multiInstanceLoopCharacteristics>
        </bpmn2:userTask>

        <bpmn2:sequenceFlow id="SequenceFlow_0ujt88d" sourceRef="UserTask_0ixdrmt" targetRef="EndEvent_0obkkv6"/>



        <bpmn2:userTask id="UserTask_1y14uzz" name="职务2">
            <bpmn2:incoming>SequenceFlow_1pshchp</bpmn2:incoming>
            <bpmn2:outgoing>SequenceFlow_0yqwm48</bpmn2:outgoing>
            <bpmn2:multiInstanceLoopCharacteristics >
                <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression">${nrOfCompletedInstances >= 1}
                </bpmn2:completionCondition>
            </bpmn2:multiInstanceLoopCharacteristics>
        </bpmn2:userTask>



        <bpmn2:userTask id="UserTask_1uhyq9y" name="职务4">
            <bpmn2:incoming>SequenceFlow_0yqwm48</bpmn2:incoming>
            <bpmn2:outgoing>SequenceFlow_0rvinyg</bpmn2:outgoing>
            <bpmn2:multiInstanceLoopCharacteristics >
                <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression">${nrOfCompletedInstances >= 1}
                </bpmn2:completionCondition>
            </bpmn2:multiInstanceLoopCharacteristics>
        </bpmn2:userTask>
        <bpmn2:sequenceFlow id="SequenceFlow_0yqwm48" sourceRef="UserTask_1y14uzz" targetRef="UserTask_1uhyq9y"/>

        <bpmn2:sequenceFlow id="SequenceFlow_0rvinyg" sourceRef="UserTask_1uhyq9y" targetRef="EndEvent_0obkkv6"/>

        <bpmn2:endEvent id="EndEvent_0obkkv6">
            <bpmn2:incoming>SequenceFlow_0ujt88d</bpmn2:incoming>
            <bpmn2:incoming>SequenceFlow_0rvinyg</bpmn2:incoming>
        </bpmn2:endEvent>
    </bpmn2:process>

</bpmn2:definitions>