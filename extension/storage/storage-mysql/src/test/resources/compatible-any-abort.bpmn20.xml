<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL"
                   xmlns:alibaba="http://alibaba.org/bpmn" xmlns:activiti="http://activiti.org/bpmn"
                   id="compatible-any-passed-process" version="1.0.0"
                   targetNamespace="http://bpmn.io/schema/bpmn"
                   xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL BPMN20.xsd">
    <bpmn2:process id="Process_1">
        <bpmn2:startEvent id="StartEvent_0iws7oy">
            <bpmn2:outgoing>SequenceFlow_003qn0i</bpmn2:outgoing>
        </bpmn2:startEvent>
        <bpmn2:sequenceFlow id="SequenceFlow_003qn0i" sourceRef="StartEvent_0iws7oy" targetRef="userTask1"/>

        <bpmn2:userTask id="userTask1" name="1个" >
            <bpmn2:multiInstanceLoopCharacteristics id="userTask1" name="1个">
                <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression"><![CDATA[${nrOfCompletedInstances  >= 2]]></bpmn2:completionCondition>
                <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression" action="abort"><![CDATA[${nrOfRejectedInstance  >= 1 ]]></bpmn2:completionCondition>
            </bpmn2:multiInstanceLoopCharacteristics>
        </bpmn2:userTask>

        <bpmn2:sequenceFlow id="SequenceFlow_0ujt88d" sourceRef="userTask1" targetRef="EndEvent_0obkkv6"/>


        <bpmn2:endEvent id="EndEvent_0obkkv6">
        </bpmn2:endEvent>
    </bpmn2:process>

</bpmn2:definitions>