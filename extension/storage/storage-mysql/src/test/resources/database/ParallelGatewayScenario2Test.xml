<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" 
    xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" 
    xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" 
    xmlns:smart="http://smartengine.org/schema/process" 
    xmlns:di="http://www.omg.org/spec/DD/20100524/DI" 
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
    targetNamespace="smart">
    
    <process id="parallel-scenario2" isExecutable="true">
        <startEvent id="start" name="开始"/>
        <parallelGateway id="fork" name="Fork事件"/>
        <parallelGateway id="join" name="Join事件"/>
        
        <!-- 分支1：直接进入join -->
        <serviceTask id="service1" name="服务任务1" smart:class="serviceTaskDelegation"/>
        <serviceTask id="service2" name="服务任务2" smart:class="serviceTaskDelegation"/>
        
        <!-- 分支2：进入receiveTask -->
        <receiveTask id="receiveTask1" name="接收任务"/>
        <serviceTask id="service3" name="服务任务3" smart:class="serviceTaskDelegation"/>
        
        <endEvent id="end" name="结束"/>

        <!-- 流程连接 -->
        <sequenceFlow id="flow1" sourceRef="start" targetRef="fork"/>
        
        <!-- 分支1 -->
        <sequenceFlow id="flow2" sourceRef="fork" targetRef="service1"/>
        <sequenceFlow id="flow3" sourceRef="service1" targetRef="service2"/>
        <sequenceFlow id="flow4" sourceRef="service2" targetRef="join"/>
        
        <!-- 分支2 -->
        <sequenceFlow id="flow5" sourceRef="fork" targetRef="receiveTask1"/>
        <sequenceFlow id="flow6" sourceRef="receiveTask1" targetRef="service3"/>
        <sequenceFlow id="flow7" sourceRef="service3" targetRef="join"/>
        
        <!-- 结束 -->
        <sequenceFlow id="flow8" sourceRef="join" targetRef="end"/>
    </process>
    
    <!-- 省略BPMNDiagram部分，可参考现有文件格式 -->
</definitions>