<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:smart="http://smart-framework/schema"
             xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
             xmlns:dc="http://www.omg.org/spec/DD/20100524/DC"
             xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
             targetNamespace="http://smart-framework/schema">
    <process id="inclusiveGatewayNestedTest">
        <startEvent id="start" />
        <inclusiveGateway id="mainFork" />
        <sequenceFlow id="flow_start_to_mainFork" sourceRef="start" targetRef="mainFork" />

        <!-- 主分支1 -->
        <sequenceFlow id="flow_mainFork_to_subFork1" sourceRef="mainFork" targetRef="subFork1">
            <conditionExpression xsi:type="tFormalExpression">${mainCondition1 == true}</conditionExpression>
        </sequenceFlow>
        <inclusiveGateway id="subFork1" />

        <!-- 子分支1-1 -->
        <sequenceFlow id="flow_subFork1_to_subService1_1" sourceRef="subFork1" targetRef="subService1_1">
            <conditionExpression xsi:type="tFormalExpression">${subCondition1_1 == true}</conditionExpression>
        </sequenceFlow>
        <serviceTask id="subService1_1" />
        <sequenceFlow id="flow_subService1_1_to_subJoin1" sourceRef="subService1_1" targetRef="subJoin1" />

        <!-- 子分支1-2 -->
        <sequenceFlow id="flow_subFork1_to_subService1_2" sourceRef="subFork1" targetRef="subService1_2">
            <conditionExpression xsi:type="tFormalExpression">${subCondition1_2 == true}</conditionExpression>
        </sequenceFlow>
        <serviceTask id="subService1_2" />
        <sequenceFlow id="flow_subService1_2_to_subJoin1" sourceRef="subService1_2" targetRef="subJoin1" />

        <inclusiveGateway id="subJoin1" />
        <sequenceFlow id="flow_subJoin1_to_mainJoin" sourceRef="subJoin1" targetRef="mainJoin" />

        <!-- 主分支2 -->
        <sequenceFlow id="flow_mainFork_to_subFork2" sourceRef="mainFork" targetRef="subFork2">
            <conditionExpression xsi:type="tFormalExpression">${mainCondition2 == true}</conditionExpression>
        </sequenceFlow>
        <inclusiveGateway id="subFork2" />

        <!-- 子分支2-1 -->
        <sequenceFlow id="flow_subFork2_to_subService2_1" sourceRef="subFork2" targetRef="subService2_1">
            <conditionExpression xsi:type="tFormalExpression">${subCondition2_1 == true}</conditionExpression>
        </sequenceFlow>
        <serviceTask id="subService2_1" />
        <sequenceFlow id="flow_subService2_1_to_subJoin2" sourceRef="subService2_1" targetRef="subJoin2" />

        <!-- 子分支2-2 -->
        <sequenceFlow id="flow_subFork2_to_subService2_2" sourceRef="subFork2" targetRef="subService2_2">
            <conditionExpression xsi:type="tFormalExpression">${subCondition2_2 == true}</conditionExpression>
        </sequenceFlow>
        <serviceTask id="subService2_2" />
        <sequenceFlow id="flow_subService2_2_to_subJoin2" sourceRef="subService2_2" targetRef="subJoin2" />

        <inclusiveGateway id="subJoin2" />
        <sequenceFlow id="flow_subJoin2_to_mainJoin" sourceRef="subJoin2" targetRef="mainJoin" />

        <!-- 主join -->
        <inclusiveGateway id="mainJoin" />
        <sequenceFlow id="flow_mainJoin_to_end" sourceRef="mainJoin" targetRef="end" />

        <endEvent id="end" />
    </process>
</definitions>