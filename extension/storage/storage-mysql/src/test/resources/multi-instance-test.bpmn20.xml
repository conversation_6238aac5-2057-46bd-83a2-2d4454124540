<definitions xmlns:smart="http://smartengine.org/schema/process" xmlns:activiti="http://activiti.org/bpmn"
             xmlns:alibaba="http://smart.alibaba.com/schema/alibaba"
             id="test-multi-instance-user-task" version="1.0.1"
             xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             targetNamespace="Examples">

    <process id="multi-instance-user-task" version="1.0.2">

        <startEvent id="theStart"/>
        <sequenceFlow id="flow1" sourceRef="theStart" targetRef="multiUserTask"/>

        <userTask id="multiUserTask" name="My User Task"  smart:class="userTaskTestDelegation">

            <multiInstanceLoopCharacteristics  isSequential="false">
                <completionCondition><![CDATA[nrOfCompletedInstances >=1]]></completionCondition>
            </multiInstanceLoopCharacteristics>
        </userTask>

        <sequenceFlow id="flow2" sourceRef="multiUserTask" targetRef="normalReceiverTask"/>

        <receiveTask id="normalReceiverTask"/>

        <sequenceFlow id="flow3" sourceRef="normalReceiverTask" targetRef="normalUserTask"/>

        <userTask id="normalUserTask" name="Normal User Task"
                  smart:class="userTaskTestDelegation">

        </userTask>

        <sequenceFlow id="flow4" sourceRef="normalUserTask" targetRef="end"/>

        <endEvent id="end"/>


    </process>

</definitions>