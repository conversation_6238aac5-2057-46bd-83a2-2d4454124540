<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
                   xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
                   xmlns:alibaba="http://alibaba.org/bpmn" xmlns:activiti="http://activiti.org/bpmn"
                   id="JBSP-2017102322057" targetNamespace="http://bpmn.io/schema/bpmn"
                   xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL BPMN20.xsd">
    <bpmn2:process id="Process_1">
        <bpmn2:startEvent id="StartEvent_0iznuq2">
            <bpmn2:outgoing>SequenceFlow_1i5tbfp</bpmn2:outgoing>
        </bpmn2:startEvent>
        <bpmn2:userTask >
            <bpmn2:documentation>dddd</bpmn2:documentation>
            <bpmn2:incoming>SequenceFlow_1i5tbfp</bpmn2:incoming>
            <bpmn2:outgoing>SequenceFlow_0ah1ilq</bpmn2:outgoing>
            <bpmn2:multiInstanceLoopCharacteristics>
                <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression">${nrOfCompletedInstances >= 1}
                </bpmn2:completionCondition>
            </bpmn2:multiInstanceLoopCharacteristics>
        </bpmn2:userTask>
        <bpmn2:endEvent id="EndEvent_0kf021k">
            <bpmn2:incoming>SequenceFlow_0ah1ilq</bpmn2:incoming>
        </bpmn2:endEvent>
        <bpmn2:sequenceFlow id="SequenceFlow_1i5tbfp" sourceRef="StartEvent_0iznuq2" targetRef="UserTask_0r8rr68"/>
        <bpmn2:sequenceFlow id="SequenceFlow_0ah1ilq" sourceRef="UserTask_0r8rr68" targetRef="EndEvent_0kf021k"/>
    </bpmn2:process>
    <bpmndi:BPMNDiagram id="BPMNDiagram_1">
        <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">
            <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2">
                <dc:Bounds x="412" y="240" width="36" height="36"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="StartEvent_0iznuq2_di" bpmnElement="StartEvent_0iznuq2">
                <dc:Bounds x="358" y="250" width="36" height="36"/>
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="331" y="286" width="90" height="20"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="UserTask_0r8rr68_di" bpmnElement="UserTask_0r8rr68">
                <dc:Bounds x="464" y="218" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="EndEvent_0kf021k_di" bpmnElement="EndEvent_0kf021k">
                <dc:Bounds x="677" y="240" width="36" height="36"/>
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="650" y="276" width="90" height="20"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_1i5tbfp_di" bpmnElement="SequenceFlow_1i5tbfp">
                <di:waypoint xsi:type="dc:Point" x="394" y="268"/>
                <di:waypoint xsi:type="dc:Point" x="429" y="268"/>
                <di:waypoint xsi:type="dc:Point" x="429" y="258"/>
                <di:waypoint xsi:type="dc:Point" x="464" y="258"/>
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="384" y="253" width="90" height="20"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_0ah1ilq_di" bpmnElement="SequenceFlow_0ah1ilq">
                <di:waypoint xsi:type="dc:Point" x="564" y="258"/>
                <di:waypoint xsi:type="dc:Point" x="677" y="258"/>
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="575.5" y="248" width="90" height="20"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</bpmn2:definitions>