<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
             xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
             xmlns:smart="http://smartengine.org/schema/process"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" targetNamespace="smart" version="1.0.0" id="CREDIT_APPLICATION">
    <process id="CreditApplication_process" name="授信申请流程" isExecutable="false">
        <startEvent id="StartEvent_1" />

        <sequenceFlow id="SequenceFlow_16ifyt3" name="提交机构准入" sourceRef="StartEvent_1" targetRef="InstitutionAdmit">
            <extensionElements>
                <smart:executionListener
                        class="com.alibaba.smart.framework.engine.modules.smart.TccTracker"
                        event="TRANSITION_EXECUTE">
                </smart:executionListener>
            </extensionElements>
        </sequenceFlow>

        <receiveTask id="InstitutionAdmit" name="机构准入中" >
            <extensionElements>
                <smart:value name="newState" value="ADMITTING"/>
                <smart:executionListener
                        class="com.alibaba.smart.framework.engine.modules.smart.TccTracker"
                        event="ACTIVITY_START">
                </smart:executionListener>
            </extensionElements>
        </receiveTask>

        <exclusiveGateway id="CreditInvestigationInvalidDecision" name="是否征信授权过期" />
        <sequenceFlow id="SequenceFlow_1bqdwqy" name="是" sourceRef="CreditInvestigationInvalidDecision" targetRef="SubmitEnterpriseData" />
        <receiveTask id="SubmitEnterpriseData" name="企业资料上传" />
        <parallelGateway id="ParallelGateway_1xmiyxg" />
        <sequenceFlow id="SequenceFlow_1e12vlt" sourceRef="SubmitEnterpriseData" targetRef="ParallelGateway_1xmiyxg" />
        <receiveTask id="EnterpriseDataAudit" name="企业资料审核" />
        <sequenceFlow id="SequenceFlow_1r2mr47" name="提交企业资料审核" sourceRef="ParallelGateway_1xmiyxg" targetRef="EnterpriseDataAudit" />
        <parallelGateway id="ParallelGateway_03i36sa" />
        <sequenceFlow id="SequenceFlow_18vex3l" name="审核通过" sourceRef="EnterpriseDataAudit" targetRef="ParallelGateway_03i36sa" />
        <endEvent id="EndEvent_1rkvtqo" />
        <sequenceFlow id="SequenceFlow_1li7wg6" name="准入失败" sourceRef="InstitutionAdmit" targetRef="EndEvent_1rkvtqo" />
        <sequenceFlow id="SequenceFlow_0m5x99n" name="起草协议" sourceRef="ParallelGateway_1xmiyxg" targetRef="WaitSignEnterpriseCredit" />
        <sequenceFlow id="SequenceFlow_0aksucr" name="签署完成" sourceRef="WaitSignEnterpriseCredit" targetRef="ParallelGateway_03i36sa" />
        <endEvent id="EndEvent_14t5cyr" />
        <sequenceFlow id="SequenceFlow_0o4xfnh" name="授信关闭" sourceRef="EnterpriseDataAudit" targetRef="EndEvent_14t5cyr" />
        <endEvent id="EndEvent_0pk4rvw" />
        <sequenceFlow id="SequenceFlow_0r09dr8" name="授信关闭" sourceRef="WaitSignEnterpriseCredit" targetRef="EndEvent_0pk4rvw" />
        <exclusiveGateway id="CustomerAdmitttanceDecision" name="客户准入是否完成" />
        <sequenceFlow id="SequenceFlow_0vcq3d4" sourceRef="ParallelGateway_03i36sa" targetRef="CustomerAdmitttanceDecision" />
        <receiveTask id="WaitCustomerAdmittance" name="待客户资料审核" />
        <sequenceFlow id="SequenceFlow_0dksxky" name="否" sourceRef="CustomerAdmitttanceDecision" targetRef="WaitCustomerAdmittance" />
        <endEvent id="EndEvent_10odwpm" />
        <sequenceFlow id="SequenceFlow_1ot7pwa" name="授信关闭" sourceRef="WaitCustomerAdmittance" targetRef="EndEvent_10odwpm" />
        <callActivity id="CallActivity_0mfep2s" name="机构授信" />
        <sequenceFlow id="SequenceFlow_08277if" name="是" sourceRef="CustomerAdmitttanceDecision" targetRef="CallActivity_0mfep2s" />
        <endEvent id="EndEvent_09yo502" />
        <sequenceFlow id="SequenceFlow_1d965pv" name="授信完成" sourceRef="CallActivity_0mfep2s" targetRef="EndEvent_09yo502" />
        <sequenceFlow id="SequenceFlow_02r73p5" name="审核通过" sourceRef="WaitCustomerAdmittance" targetRef="CallActivity_0mfep2s" />
        <exclusiveGateway id="EnterpriseOrPersonalDecision" name="对公还是对私" />
        <sequenceFlow id="SequenceFlow_0uo1153" sourceRef="InstitutionAdmit" targetRef="EnterpriseOrPersonalDecision" />
        <sequenceFlow id="SequenceFlow_1lh8i3i" name="对公" sourceRef="EnterpriseOrPersonalDecision" targetRef="CreditInvestigationInvalidDecision" />
        <receiveTask id="WaitSignPersonalCredit" name="待签署" />
        <receiveTask id="WaitSignEnterpriseCredit" name="待签署" />
        <sequenceFlow id="SequenceFlow_0obhmqw" name="对私" sourceRef="EnterpriseOrPersonalDecision" targetRef="WaitSignPersonalCredit" />
        <sequenceFlow id="SequenceFlow_0iq6d7p" name="签署完成" sourceRef="WaitSignPersonalCredit" targetRef="CustomerAdmitttanceDecision" />
    </process>
    <bpmndi:BPMNDiagram id="BPMNDiagram_1">
        <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="CreditApplication_process">
            <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
                <dc:Bounds x="393" y="140" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="366" y="176" width="90" height="20" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_16ifyt3_di" bpmnElement="SequenceFlow_16ifyt3">
                <di:waypoint xsi:type="dc:Point" x="411" y="176" />
                <di:waypoint xsi:type="dc:Point" x="411" y="329" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="393" y="246" width="66" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="ExclusiveGateway_05ojs59_di" bpmnElement="CreditInvestigationInvalidDecision" isMarkerVisible="true">
                <dc:Bounds x="273" y="587" width="50" height="50" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="256" y="640" width="88" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_1bqdwqy_di" bpmnElement="SequenceFlow_1bqdwqy">
                <di:waypoint xsi:type="dc:Point" x="298" y="637" />
                <di:waypoint xsi:type="dc:Point" x="298" y="715" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="308" y="676.1424657534246" width="11" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="ReceiveTask_0y6ksis_di" bpmnElement="SubmitEnterpriseData">
                <dc:Bounds x="248" y="715" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="ParallelGateway_1xmiyxg_di" bpmnElement="ParallelGateway_1xmiyxg">
                <dc:Bounds x="273" y="852" width="50" height="50" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="298" y="905" width="0" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_1e12vlt_di" bpmnElement="SequenceFlow_1e12vlt">
                <di:waypoint xsi:type="dc:Point" x="298" y="795" />
                <di:waypoint xsi:type="dc:Point" x="298" y="852" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="313" y="816.5" width="0" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="ReceiveTask_0tesk13_di" bpmnElement="EnterpriseDataAudit">
                <dc:Bounds x="135" y="1004" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_1r2mr47_di" bpmnElement="SequenceFlow_1r2mr47">
                <di:waypoint xsi:type="dc:Point" x="273" y="877" />
                <di:waypoint xsi:type="dc:Point" x="185" y="877" />
                <di:waypoint xsi:type="dc:Point" x="185" y="1004" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="141" y="858" width="88" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="ParallelGateway_03i36sa_di" bpmnElement="ParallelGateway_03i36sa">
                <dc:Bounds x="273" y="1151" width="50" height="50" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="298" y="1204" width="0" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_18vex3l_di" bpmnElement="SequenceFlow_18vex3l">
                <di:waypoint xsi:type="dc:Point" x="185" y="1084" />
                <di:waypoint xsi:type="dc:Point" x="185" y="1176" />
                <di:waypoint xsi:type="dc:Point" x="273" y="1176" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="178" y="1124" width="44" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="EndEvent_1rkvtqo_di" bpmnElement="EndEvent_1rkvtqo">
                <dc:Bounds x="605" y="351" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="465" y="491" width="90" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_1li7wg6_di" bpmnElement="SequenceFlow_1li7wg6">
                <di:waypoint xsi:type="dc:Point" x="461" y="369" />
                <di:waypoint xsi:type="dc:Point" x="533" y="369" />
                <di:waypoint xsi:type="dc:Point" x="533" y="369" />
                <di:waypoint xsi:type="dc:Point" x="605" y="369" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="475" y="347" width="44" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_0m5x99n_di" bpmnElement="SequenceFlow_0m5x99n">
                <di:waypoint xsi:type="dc:Point" x="323" y="877" />
                <di:waypoint xsi:type="dc:Point" x="479" y="877" />
                <di:waypoint xsi:type="dc:Point" x="479" y="1004" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="420" y="856" width="44" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_0aksucr_di" bpmnElement="SequenceFlow_0aksucr">
                <di:waypoint xsi:type="dc:Point" x="479" y="1084" />
                <di:waypoint xsi:type="dc:Point" x="479" y="1176" />
                <di:waypoint xsi:type="dc:Point" x="323" y="1176" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="472" y="1124" width="44" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="EndEvent_14t5cyr_di" bpmnElement="EndEvent_14t5cyr">
                <dc:Bounds x="-4" y="1026" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="14" y="1065" width="0" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_0o4xfnh_di" bpmnElement="SequenceFlow_0o4xfnh">
                <di:waypoint xsi:type="dc:Point" x="135" y="1044" />
                <di:waypoint xsi:type="dc:Point" x="32" y="1044" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="62" y="1022" width="44" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="EndEvent_0pk4rvw_di" bpmnElement="EndEvent_0pk4rvw">
                <dc:Bounds x="319" y="1026" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="337" y="1065" width="0" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_0r09dr8_di" bpmnElement="SequenceFlow_0r09dr8">
                <di:waypoint xsi:type="dc:Point" x="429" y="1044" />
                <di:waypoint xsi:type="dc:Point" x="355" y="1044" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="370" y="1023" width="44" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="ExclusiveGateway_08pbu5y_di" bpmnElement="CustomerAdmitttanceDecision" isMarkerVisible="true">
                <dc:Bounds x="273" y="1279" width="50" height="50" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="254" y="1332" width="88" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_0vcq3d4_di" bpmnElement="SequenceFlow_0vcq3d4">
                <di:waypoint xsi:type="dc:Point" x="298" y="1201" />
                <di:waypoint xsi:type="dc:Point" x="298" y="1279" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="313" y="1233" width="0" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="ReceiveTask_0bpoca3_di" bpmnElement="WaitCustomerAdmittance">
                <dc:Bounds x="457" y="1331" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_0dksxky_di" bpmnElement="SequenceFlow_0dksxky">
                <di:waypoint xsi:type="dc:Point" x="298" y="1329" />
                <di:waypoint xsi:type="dc:Point" x="298" y="1371" />
                <di:waypoint xsi:type="dc:Point" x="457" y="1371" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="374.0921052631579" y="1350" width="11" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="EndEvent_10odwpm_di" bpmnElement="EndEvent_10odwpm">
                <dc:Bounds x="660" y="1353" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="678" y="1392" width="0" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_1ot7pwa_di" bpmnElement="SequenceFlow_1ot7pwa">
                <di:waypoint xsi:type="dc:Point" x="557" y="1371" />
                <di:waypoint xsi:type="dc:Point" x="595" y="1371" />
                <di:waypoint xsi:type="dc:Point" x="595" y="1371" />
                <di:waypoint xsi:type="dc:Point" x="660" y="1371" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="605.5" y="1349" width="44" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="CallActivity_0mfep2s_di" bpmnElement="CallActivity_0mfep2s">
                <dc:Bounds x="248" y="1439" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_08277if_di" bpmnElement="SequenceFlow_08277if">
                <di:waypoint xsi:type="dc:Point" x="298" y="1329" />
                <di:waypoint xsi:type="dc:Point" x="298" y="1439" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="255" y="1386" width="11" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="EndEvent_09yo502_di" bpmnElement="EndEvent_09yo502">
                <dc:Bounds x="280" y="1615" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="298" y="1654" width="0" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_1d965pv_di" bpmnElement="SequenceFlow_1d965pv">
                <di:waypoint xsi:type="dc:Point" x="298" y="1519" />
                <di:waypoint xsi:type="dc:Point" x="298" y="1615" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="291" y="1560" width="44" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_02r73p5_di" bpmnElement="SequenceFlow_02r73p5">
                <di:waypoint xsi:type="dc:Point" x="489" y="1411" />
                <di:waypoint xsi:type="dc:Point" x="489" y="1479" />
                <di:waypoint xsi:type="dc:Point" x="348" y="1479" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="482" y="1438.5" width="44" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="ExclusiveGateway_006eavs_di" bpmnElement="EnterpriseOrPersonalDecision" isMarkerVisible="true">
                <dc:Bounds x="386" y="496.2950530035336" width="50" height="50" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="380" y="555" width="66" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_0uo1153_di" bpmnElement="SequenceFlow_0uo1153">
                <di:waypoint xsi:type="dc:Point" x="411" y="409" />
                <di:waypoint xsi:type="dc:Point" x="411" y="496" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="381" y="446" width="90" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_1lh8i3i_di" bpmnElement="SequenceFlow_1lh8i3i">
                <di:waypoint xsi:type="dc:Point" x="386" y="521" />
                <di:waypoint xsi:type="dc:Point" x="298" y="521" />
                <di:waypoint xsi:type="dc:Point" x="298" y="587" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="331" y="500" width="22" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="ReceiveTask_014754j_di" bpmnElement="WaitSignPersonalCredit">
                <dc:Bounds x="573" y="714.6060070671379" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="ReceiveTask_1ev2x38_di" bpmnElement="WaitSignEnterpriseCredit">
                <dc:Bounds x="429" y="1004" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_0obhmqw_di" bpmnElement="SequenceFlow_0obhmqw">
                <di:waypoint xsi:type="dc:Point" x="436" y="521" />
                <di:waypoint xsi:type="dc:Point" x="623" y="521" />
                <di:waypoint xsi:type="dc:Point" x="623" y="715" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="519" y="500" width="22" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_0iq6d7p_di" bpmnElement="SequenceFlow_0iq6d7p">
                <di:waypoint xsi:type="dc:Point" x="623" y="795" />
                <di:waypoint xsi:type="dc:Point" x="623" y="1304" />
                <di:waypoint xsi:type="dc:Point" x="323" y="1304" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="616" y="1043.7313218390805" width="44" height="13" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="ReceiveTask_0b80zbo_di" bpmnElement="InstitutionAdmit">
                <dc:Bounds x="361" y="329" width="100" height="80" />
            </bpmndi:BPMNShape>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</definitions>
